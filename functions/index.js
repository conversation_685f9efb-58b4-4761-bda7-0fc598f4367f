import { initializeApp } from "firebase-admin/app"

initializeApp()

// ANALYTICS

export { getanalyticsdata } from "./firebaseFunctions/analytics/getanalyticsdata.js"
export { getbillinganalyticsdata } from "./firebaseFunctions/analytics/getbillinganalyticsdata.js"
export { getinvoiceanalyticsdata } from "./firebaseFunctions/analytics/getinvoiceanalyticsdata.js"

// PROJECTS

export { createnote } from "./firebaseFunctions/projects/createnote.js"
export { getprojects } from "./firebaseFunctions/projects/getprojects.js"
export { getprojecttypelist } from "./firebaseFunctions/projects/getprojecttypelist.js"
export { getprojecttypephaselist } from "./firebaseFunctions/projects/getprojecttypephaselist.js"
export { getprojectvitals } from "./firebaseFunctions/projects/getprojectvitals.js"
export { syncProjectTypePhaseList } from "./firebaseFunctions/projects/syncProjectTypePhaseList.js"
export { updateproject } from "./firebaseFunctions/projects/updateproject.js"

// AUTOMATIONS

export { automationenabledchecker } from "./firebaseFunctions/automations/automationenabledchecker.js"
export { automationswitchhandler } from "./firebaseFunctions/automations/automationswitchhandler.js"
export { createcollectionmap } from "./firebaseFunctions/automations/createcollectionmap.js"
export { deletecustomautomationdata } from "./firebaseFunctions/automations/deletecustomautomationdata.js"
export { getprojecttypesectionlist } from "./firebaseFunctions/automations/getprojecttypesectionlist.js"
export { getcustomautomationdata } from "./firebaseFunctions/automations/getcustomautomationdata.js"
export { getindidualautomationdata } from "./firebaseFunctions/automations/getindidualautomationdata.js"
export { getautomationsectionsfieldlist } from "./firebaseFunctions/automations/getautomationsectionsfieldlist.js"
export { setcustomautomationstatus } from "./firebaseFunctions/automations/setcustomautomationstatus.js"

// FORM CHANGES

export { setupFormChangeTracking } from "./firebaseFunctions/formChanges/setupFormChangeTracking.js"
export { handleFormChange } from "./firebaseFunctions/formChanges/handleFormChange.js"
export { getFormChanges } from "./firebaseFunctions/formChanges/getFormChanges.js"
export { getFormChangeDetails } from "./firebaseFunctions/formChanges/getFormChangeDetails.js"
export { toggleFormChangeTracking } from "./firebaseFunctions/formChanges/toggleFormChangeTracking.js"

// LEGALWARE AUTOMATION

export { legalwareautomation } from "./firebaseFunctions/legalwareAutomation/legalwareautomation.js"

// SETTINGS

export { gettimecodemap } from "./firebaseFunctions/settings/gettimecodemap.js"
export { postprojecttypephaselist } from "./firebaseFunctions/settings/postprojecttypephaselist.js"

// USER

export { createuser } from "./firebaseFunctions/user/createuser.js"
export { getuser } from "./firebaseFunctions/user/getuser.js"
export { sumbmittenant } from "./firebaseFunctions/user/sumbmittenant.js"
export { updateuser } from "./firebaseFunctions/user/updateuser.js"

// SUPPORT REQUEST

export { getSupportTickets } from "./firebaseFunctions/supportRequest/getSupportTickets.js"
export { createSupportTicket } from "./firebaseFunctions/supportRequest/createSupportTicket.js"
export { getSupportProjectPrimary } from "./firebaseFunctions/supportRequest/getSupportProjectPrimary.js"
