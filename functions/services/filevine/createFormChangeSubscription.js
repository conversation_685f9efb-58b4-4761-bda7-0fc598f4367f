/**
 * Creates a webhook subscription for form changes in Filevine
 *
 * This service sets up a webhook subscription to track form changes
 * and sends the events to a specified endpoint.
 */

/**
 * Creates a webhook subscription for form changes
 *
 * @param {string} accessToken - Filevine API access token
 * @param {string} refreshToken - Filevine API refresh token
 * @param {object} creds - Credentials object containing tenant, orgId, etc.
 * @param {string} endpoint - The endpoint URL where webhook events will be sent
 * @returns {Promise<object>} - The created subscription object
 */
const createFormChangeSubscription = async (accessToken, refreshToken, creds, endpoint) => {
  let baseUrl = ""
  const headersObj = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`
  }

  if (creds.pat) {
    baseUrl = "https://api.filevineapp.com/fv-app/v2/webhooks/subscription"
    headersObj["x-fv-orgid"] = creds.orgId
    headersObj["x-fv-userid"] = creds.serviceAcctUserId
  } else {
    baseUrl = `https://${creds.tenant}.api.${creds.type}.com/subscriptions`
    headersObj["x-fv-sessionid"] = refreshToken
  }

  // Create subscription body with form change events
  const subscriptionBody = {
    name: "Form Change Tracker",
    description: "Tracks changes in forms and stores them in Firebase",
    endpoint: endpoint,
    eventIds: ["StaticSection.Updated"]
  }

  // Add keyId if not using PAT
  if (!creds.pat) {
    subscriptionBody.keyId = creds.key
  }

  // Log the subscription request details
  console.log("=== CREATING FORM CHANGE SUBSCRIPTION ===")
  console.log("Base URL:", baseUrl)
  console.log("Subscription Body:", JSON.stringify(subscriptionBody, null, 2))
  console.log("Headers:", JSON.stringify(headersObj, null, 2))

  let response = await fetch(baseUrl, {
    method: "POST",
    mode: "cors",
    headers: headersObj,
    body: JSON.stringify(subscriptionBody)
  })

  console.log("Response Status:", response.status)
  console.log("Response Status Text:", response.statusText)

  if (response.status >= 300) {
    const errorText = await response.text()
    console.error(`${response.status} ${response.statusText} | Error at createFormChangeSubscription`)
    console.error("Error Response Body:", errorText)
    throw new Error(`Failed to create form change subscription: ${response.status} ${response.statusText}`)
  } else {
    const responseData = await response.json()
    console.log("Subscription Created Successfully:", JSON.stringify(responseData, null, 2))
    console.log("=== END SUBSCRIPTION CREATION ===")
    return responseData
  }
}

export default createFormChangeSubscription
