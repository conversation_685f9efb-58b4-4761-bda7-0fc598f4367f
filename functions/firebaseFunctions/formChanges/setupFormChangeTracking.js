/**
 * Sets up form change tracking by creating a webhook subscription
 *
 * This function creates a webhook subscription to track form changes
 * and stores the subscription ID in Firestore.
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import createFormChangeSubscription from "../../services/filevine/createFormChangeSubscription.js"

export const setupFormChangeTracking = v2.https.onCall(async (request) => {
  // Check authentication
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const db = getFirestore()
  const userEmail = request.auth.token.email

  // Get user credentials from Firestore
  let fvData = null
  try {
    fvData = await getCredsFromFirestore(userEmail)
  } catch (error) {
    console.error(error, "Error getting data from Firestore")
    throw new v2.https.HttpsError("internal", "Error retrieving user credentials")
  }

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  // Check if form change tracking is already set up
  const formChangeConfigRef = db.collection(fvData.tenant).doc("formChangeConfig")
  const formChangeConfigDoc = await formChangeConfigRef.get()

  if (formChangeConfigDoc.exists && formChangeConfigDoc.data().subscriptionId) {
    return {
      success: true,
      message: "Form change tracking is already set up",
      subscriptionId: formChangeConfigDoc.data().subscriptionId
    }
  }

  // Create session with Filevine
  let accessToken, refreshToken
  try {
    if (fvData.pat) {
      const sessionData = await createSessionPAT(fvData)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(fvData)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }
  } catch (error) {
    console.error(error, "Error creating session")
    throw new v2.https.HttpsError("internal", "Error creating session with Filevine")
  }

  // Create the webhook endpoint URL
  // This should be a Cloud Function HTTP endpoint that will receive the webhook events
  const endpoint = `https://formchangehandler-${process.env.FUNCTION_REGION}-${process.env.PROJECT_ID}.cloudfunctions.net/${fvData.tenant}`

  // Create the webhook subscription
  let subscriptionData
  try {
    subscriptionData = await createFormChangeSubscription(accessToken, refreshToken, fvData, endpoint)
  } catch (error) {
    console.error(error, "Error creating form change subscription")
    throw new v2.https.HttpsError("internal", "Error creating form change subscription")
  }

  // Get the subscription ID based on whether PAT is used or not
  const subscriptionId = fvData.pat ? subscriptionData.id : subscriptionData.subscriptionId

  // Store the subscription ID in Firestore
  try {
    await formChangeConfigRef.set({
      subscriptionId: subscriptionId,
      enabled: true,
      createdAt: new Date().toISOString(),
      createdBy: userEmail
    })
  } catch (error) {
    console.error(error, "Error storing subscription ID in Firestore")
    throw new v2.https.HttpsError("internal", "Error storing subscription configuration")
  }

  return {
    success: true,
    message: "Form change tracking has been set up successfully",
    subscriptionId: subscriptionId
  }
})
