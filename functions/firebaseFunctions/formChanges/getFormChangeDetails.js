/**
 * Retrieves detailed information about a specific form change
 * 
 * This function fetches the current form data and compares it with
 * the recorded change to show what fields were changed and their values.
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getForm from "../../services/filevine/getForm.js"

export const getFormChangeDetails = v2.https.onCall(async (request) => {
  // Check authentication
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const db = getFirestore()
  const userEmail = request.auth.token.email
  
  // Get user credentials from Firestore
  let fvData = null
  try {
    fvData = await getCredsFromFirestore(userEmail)
  } catch (error) {
    console.error(error, "Error getting data from Firestore")
    throw new v2.https.HttpsError("internal", "Error retrieving user credentials")
  }

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  // Get parameters from the request
  const { projectId, changeId } = request.data || {}
  
  if (!projectId || !changeId) {
    throw new v2.https.HttpsError("invalid-argument", "Both projectId and changeId are required")
  }

  // Get the form change record from Firestore
  const changeDocRef = db.collection(fvData.tenant)
    .doc("formChanges")
    .collection("projects")
    .doc(projectId)
    .collection("changes")
    .doc(changeId)
  
  const changeDoc = await changeDocRef.get()
  
  if (!changeDoc.exists) {
    throw new v2.https.HttpsError("not-found", "Form change record not found")
  }
  
  const changeData = changeDoc.data()

  // Create session with Filevine
  let accessToken, refreshToken
  try {
    if (fvData.pat) {
      const sessionData = await createSessionPAT(fvData)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(fvData)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }
  } catch (error) {
    console.error(error, "Error creating session")
    throw new v2.https.HttpsError("internal", "Error creating session with Filevine")
  }

  // Get the current form data
  let currentFormData
  try {
    currentFormData = await getForm(
      accessToken, 
      refreshToken, 
      fvData, 
      projectId, 
      changeData.sectionSelector
    )
  } catch (error) {
    console.error(error, "Error getting current form data")
    throw new v2.https.HttpsError("internal", "Error retrieving current form data")
  }

  // Get the previous form data if available
  let previousFormData = null
  const previousChangeQuery = await db.collection(fvData.tenant)
    .doc("formChanges")
    .collection("projects")
    .doc(projectId)
    .collection("changes")
    .where("sectionSelector", "==", changeData.sectionSelector)
    .where("timestamp", "<", changeData.timestamp)
    .orderBy("timestamp", "desc")
    .limit(1)
    .get()
  
  if (!previousChangeQuery.empty) {
    const previousChangeId = previousChangeQuery.docs[0].id
    
    // Get the form data at the time of the previous change
    const previousFormDataDoc = await db.collection(fvData.tenant)
      .doc("formChanges")
      .collection("projects")
      .doc(projectId)
      .collection("formData")
      .doc(previousChangeId)
      .get()
    
    if (previousFormDataDoc.exists) {
      previousFormData = previousFormDataDoc.data()
    }
  }

  // Prepare the detailed change information
  const detailedChanges = []
  
  // If we have the list of changed fields, use it
  if (changeData.changedFields && changeData.changedFields.length > 0) {
    for (const fieldSelector of changeData.changedFields) {
      const currentValue = currentFormData[fieldSelector]
      const previousValue = previousFormData ? previousFormData[fieldSelector] : null
      
      detailedChanges.push({
        fieldSelector,
        fieldName: getFieldName(currentFormData, fieldSelector),
        currentValue,
        previousValue,
        changed: JSON.stringify(currentValue) !== JSON.stringify(previousValue)
      })
    }
  } else {
    // If we don't have the list of changed fields, compare all fields
    for (const fieldSelector in currentFormData) {
      if (typeof currentFormData[fieldSelector] !== 'function' && fieldSelector !== 'name') {
        const currentValue = currentFormData[fieldSelector]
        const previousValue = previousFormData ? previousFormData[fieldSelector] : null
        
        if (JSON.stringify(currentValue) !== JSON.stringify(previousValue)) {
          detailedChanges.push({
            fieldSelector,
            fieldName: getFieldName(currentFormData, fieldSelector),
            currentValue,
            previousValue
          })
        }
      }
    }
  }

  return {
    success: true,
    changeData,
    detailedChanges
  }
})

/**
 * Helper function to get a human-readable field name
 */
function getFieldName(formData, fieldSelector) {
  // Try to get the field name from the form metadata if available
  if (formData.fieldNames && formData.fieldNames[fieldSelector]) {
    return formData.fieldNames[fieldSelector]
  }
  
  // Otherwise, format the field selector to be more readable
  return fieldSelector
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
    .trim()
}
