/**
 * Retrieves form changes from Firestore
 * 
 * This function allows retrieving form change history for a specific project,
 * or recent changes across all projects.
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"

export const getFormChanges = v2.https.onCall(async (request) => {
  // Check authentication
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const db = getFirestore()
  const userEmail = request.auth.token.email
  
  // Get user credentials from Firestore
  let fvData = null
  try {
    fvData = await getCredsFromFirestore(userEmail)
  } catch (error) {
    console.error(error, "Error getting data from Firestore")
    throw new v2.https.HttpsError("internal", "Error retrieving user credentials")
  }

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  // Check if form change tracking is set up
  const formChangeConfigRef = db.collection(fvData.tenant).doc("formChangeConfig")
  const formChangeConfigDoc = await formChangeConfigRef.get()
  
  if (!formChangeConfigDoc.exists || !formChangeConfigDoc.data().enabled) {
    return {
      success: false,
      message: "Form change tracking is not enabled",
      changes: []
    }
  }

  // Determine what type of query to run based on request parameters
  const { projectId, limit = 50, startAfter = null } = request.data || {}

  let formChanges = []

  if (projectId) {
    // Get changes for a specific project
    let query = db.collection(fvData.tenant)
      .doc("formChanges")
      .collection("projects")
      .doc(projectId)
      .collection("changes")
      .orderBy("timestamp", "desc")
      .limit(limit)
    
    // Apply pagination if startAfter is provided
    if (startAfter) {
      query = query.startAfter(startAfter)
    }
    
    const snapshot = await query.get()
    
    formChanges = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))
  } else {
    // Get recent changes across all projects
    const formChangesDoc = await db.collection(fvData.tenant)
      .doc("formChanges")
      .get()
    
    if (formChangesDoc.exists && formChangesDoc.data().recentChanges) {
      // Sort by timestamp in descending order and limit the results
      formChanges = formChangesDoc.data().recentChanges
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, limit)
    }
  }

  return {
    success: true,
    changes: formChanges
  }
})
