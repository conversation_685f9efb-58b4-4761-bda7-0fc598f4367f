/**
 * Handles form change events from Filevine webhooks
 * 
 * This function receives webhook events for form changes,
 * processes the data, and stores it in Firestore.
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore, FieldValue } from "firebase-admin/firestore"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getForm from "../../services/filevine/getForm.js"
import getUser from "../../services/filevine/getUser.js"

/**
 * Cloud Function to handle form change webhook events
 */
export const handleFormChange = v2.https.onRequest(async (req, res) => {
  const db = getFirestore()
  
  // Log the incoming webhook event
  console.log(`Form change event received: ${req.body.Object} ${req.body.Event}`)
  
  // Extract tenant from the URL path
  const tenant = req.params[0]
  const eventBody = req.body

  // Verify this is a form change event
  if (eventBody.Object !== "Form" || eventBody.Event !== "Changed") {
    console.log("Not a form change event, ignoring")
    res.status(200).send("Event ignored - not a form change")
    return
  }

  try {
    // Check if form change tracking is enabled for this tenant
    const formChangeConfigRef = db.collection(tenant).doc("formChangeConfig")
    const formChangeConfigDoc = await formChangeConfigRef.get()
    
    if (!formChangeConfigDoc.exists || !formChangeConfigDoc.data().enabled) {
      console.log(`Form change tracking is not enabled for tenant: ${tenant}`)
      res.status(200).send("Form change tracking is not enabled")
      return
    }

    // Get tenant credentials
    const credsRef = db.collection(tenant).doc("fv-creds")
    const credsDoc = await credsRef.get()
    
    if (!credsDoc.exists) {
      console.error(`No credentials found for tenant: ${tenant}`)
      res.status(500).send("Tenant credentials not found")
      return
    }
    
    const creds = credsDoc.data()

    // Create session with Filevine
    let accessToken, refreshToken
    if (creds.pat) {
      const sessionData = await createSessionPAT(creds)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(creds)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }

    // Get user information
    const userData = await getUser(accessToken, refreshToken, creds, eventBody.UserId)
    const userFullName = `${userData.user.firstName} ${userData.user.lastName}`

    // Get form data to see what changed
    const formData = await getForm(
      accessToken, 
      refreshToken, 
      creds, 
      eventBody.ProjectId, 
      eventBody.ObjectId.SectionSelector
    )

    // Create a record of the form change
    const formChangeData = {
      projectId: eventBody.ProjectId,
      sectionSelector: eventBody.ObjectId.SectionSelector,
      sectionName: formData.name || eventBody.ObjectId.SectionSelector,
      userId: eventBody.UserId,
      userName: userFullName,
      timestamp: eventBody.Timestamp,
      formattedTimestamp: new Date(eventBody.Timestamp).toISOString(),
      changedFields: eventBody.Other.ChangedFields || []
    }

    // Store the form change in Firestore
    // Main document for quick access to recent changes
    await db.collection(tenant)
      .doc("formChanges")
      .set({
        recentChanges: FieldValue.arrayUnion(formChangeData)
      }, { merge: true })

    // Store detailed change in a subcollection for the project
    await db.collection(tenant)
      .doc("formChanges")
      .collection("projects")
      .doc(eventBody.ProjectId)
      .collection("changes")
      .add(formChangeData)

    // Also store a summary in the project document
    await db.collection(tenant)
      .doc("formChanges")
      .collection("projects")
      .doc(eventBody.ProjectId)
      .set({
        lastChange: formChangeData,
        changeCount: FieldValue.increment(1)
      }, { merge: true })

    console.log(`Form change recorded for project ${eventBody.ProjectId}, section ${eventBody.ObjectId.SectionSelector}`)
    res.status(200).send("Form change processed successfully")
  } catch (error) {
    console.error("Error processing form change:", error)
    res.status(500).send("Error processing form change")
  }
})
