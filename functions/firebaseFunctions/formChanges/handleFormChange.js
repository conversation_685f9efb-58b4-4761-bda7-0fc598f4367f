/**
 * Handles form change events from Filevine webhooks
 *
 * This function receives webhook events for form changes,
 * processes the data, and logs it for now (no Firestore until connection is confirmed).
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getForm from "../../services/filevine/getForm.js"
import getUser from "../../services/filevine/getUser.js"

/**
 * Cloud Function to handle form change webhook events
 */
export const formChangeHandler = v2.https.onRequest(async (req, res) => {
  const db = getFirestore()

  // Log the incoming webhook event for debugging
  console.error(`Form change event received: ${req.body.Object} ${req.body.Event}`)
  console.error("Full event body:", JSON.stringify(req.body, null, 2))

  // Extract tenant from the URL path
  const tenant = req.params[0]
  const eventBody = req.body

  // Verify this is a static section update event (form change)
  if (eventBody.Object !== "StaticSection" || eventBody.Event !== "Updated") {
    console.error("Not a static section update event, ignoring")
    res.status(200).send("Event ignored - not a static section update")
    return
  }

  try {
    // Get tenant credentials
    const credsRef = db.collection(tenant).doc("fv-creds")
    const credsDoc = await credsRef.get()

    if (!credsDoc.exists) {
      console.error(`No credentials found for tenant: ${tenant}`)
      res.status(500).send("Tenant credentials not found")
      return
    }

    const creds = credsDoc.data()

    // Create session with Filevine
    let accessToken, refreshToken
    if (creds.pat) {
      const sessionData = await createSessionPAT(creds)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(creds)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }

    // Get user information
    const userData = await getUser(accessToken, refreshToken, creds, eventBody.UserId)
    const userFullName = `${userData.user.firstName} ${userData.user.lastName}`

    // Get form data to see what changed
    const formData = await getForm(
      accessToken,
      refreshToken,
      creds,
      eventBody.ProjectId,
      eventBody.ObjectId.SectionSelector
    )

    // Create a record of the form change
    const formChangeData = {
      projectId: eventBody.ProjectId,
      sectionSelector: eventBody.ObjectId.SectionSelector,
      sectionName: formData.name || eventBody.ObjectId.SectionSelector,
      userId: eventBody.UserId,
      userName: userFullName,
      timestamp: eventBody.Timestamp,
      formattedTimestamp: new Date(eventBody.Timestamp).toISOString(),
      changedFields: eventBody.Other?.ChangedFields || []
    }

    // For now, just log the data to verify the connection works
    console.error("Form change detected:", JSON.stringify(formChangeData, null, 2))

    res.status(200).send("Form change processed successfully")
  } catch (error) {
    console.error("Error processing form change:", error)
    res.status(500).send("Error processing form change")
  }
})
