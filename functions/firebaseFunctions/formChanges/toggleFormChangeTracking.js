/**
 * Toggles form change tracking on or off
 * 
 * This function enables or disables form change tracking for a tenant.
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"

export const toggleFormChangeTracking = v2.https.onCall(async (request) => {
  // Check authentication
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const db = getFirestore()
  const userEmail = request.auth.token.email
  
  // Get user credentials from Firestore
  let fvData = null
  try {
    fvData = await getCredsFromFirestore(userEmail)
  } catch (error) {
    console.error(error, "Error getting data from Firestore")
    throw new v2.https.HttpsError("internal", "Error retrieving user credentials")
  }

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  // Check if form change tracking is set up
  const formChangeConfigRef = db.collection(fvData.tenant).doc("formChangeConfig")
  const formChangeConfigDoc = await formChangeConfigRef.get()
  
  if (!formChangeConfigDoc.exists || !formChangeConfigDoc.data().subscriptionId) {
    return {
      success: false,
      message: "Form change tracking is not set up yet. Please set it up first.",
      enabled: false
    }
  }

  // Get the enabled state from the request
  const { enabled } = request.data || {}
  
  if (typeof enabled !== 'boolean') {
    throw new v2.https.HttpsError("invalid-argument", "The 'enabled' parameter must be a boolean value")
  }

  // Update the enabled state in Firestore
  try {
    await formChangeConfigRef.update({
      enabled: enabled,
      updatedAt: new Date().toISOString(),
      updatedBy: userEmail
    })
  } catch (error) {
    console.error(error, "Error updating form change tracking configuration")
    throw new v2.https.HttpsError("internal", "Error updating form change tracking configuration")
  }

  return {
    success: true,
    message: `Form change tracking has been ${enabled ? 'enabled' : 'disabled'}`,
    enabled: enabled
  }
})
