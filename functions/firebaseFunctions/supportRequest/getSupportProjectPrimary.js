import * as v2 from "firebase-functions/v2"
import getProjectList from "../../services/filevine/getProjectList.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getRopsioCreds from "../../services/getRopsioCreds.js"
import getContacts from "../../services/filevine/getContacts.js"
import { TENANT_TO_PROJECT_MAP, SUPPORT_PROJECT_TYPE_ID } from "./supportTicketsConstants.js"

export const getSupportProjectPrimary = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const { tenant } = request.data

  try {
    // Get credentials
    const fvData = await getRopsioCreds()

    // Create session
    const { accessToken, refreshToken } = await createAuthSession(fvData)

    // Get all projects
    const projectList = await fetchAllProjects(accessToken, refreshToken, fvData)

    // Find support project for tenant
    const tenantProjectPrimary = findSupportProjectPrimary(projectList, tenant, SUPPORT_PROJECT_TYPE_ID)

    const teamList = []
    let hasMore = true
    let offset = 0

    while (hasMore) {
      await getContacts(accessToken, refreshToken, fvData, 1000, offset)
        .then((data) => {
          teamList.push(...data.items)
          hasMore = data.hasMore
          offset = offset + 1000
        })
        .catch((err) => {
          hasMore = false
          throw new Error("failed", "Error getting contact list", err)
        })
    }

    const primary = teamList.filter((team) => team.fullName === tenantProjectPrimary)

    return primary[0]
  } catch (error) {
    console.error("Error in getSupportTickets:", error)
    throw new v2.https.HttpsError("internal", error.message)
  }
})

async function createAuthSession(fvData) {
  if (fvData.pat) {
    const { access_token } = await createSessionPAT(fvData)
    return { accessToken: access_token, refreshToken: null }
  }

  const { accessToken, refreshToken } = await createSession(fvData)
  return { accessToken, refreshToken }
}

async function fetchAllProjects(accessToken, refreshToken, fvData) {
  const projects = []
  let hasMore = true
  let offset = 0
  const BATCH_SIZE = 1000

  while (hasMore) {
    const response = await getProjectList(accessToken, refreshToken, fvData, BATCH_SIZE, offset)
    projects.push(...response.items)
    hasMore = response.hasMore
    offset += BATCH_SIZE
  }

  return projects
}

function findSupportProjectPrimary(projectList, tenant, supportTypeId) {
  const supportProjects = projectList.filter((project) => project.projectTypeId.native === supportTypeId)

  const tenantProjectName = TENANT_TO_PROJECT_MAP[tenant]
  const tenantProject = supportProjects.find((project) => project.projectName === tenantProjectName)

  if (!tenantProject) {
    throw new Error(`Support project not found for tenant: ${tenant}`)
  }

  return tenantProject.firstPrimaryName
}
