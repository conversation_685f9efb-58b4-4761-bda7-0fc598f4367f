import * as v2 from "firebase-functions/v2"
import createCollectionItem from "../../services/filevine/createCollectionItem.js"
import getProjectList from "../../services/filevine/getProjectList.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getRopsioCreds from "../../services/getRopsioCreds.js"
import { SUPPORT_TICKETS_SELECTOR } from "./supportTicketsConstants.js"
import findTenantSupportProject from "./utils/findTenantSupportProject.js"

export const createSupportTicket = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const { tenant, dataobject } = request.data

  let body = { dataobject }

  try {
    // Get credentials
    const fvData = await getRopsioCreds()

    // Create session
    const { accessToken, refreshToken } = await createAuthSession(fvData)

    // Get all projects
    const projectList = await fetchAllProjects(accessToken, refreshToken, fvData)

    // Find support project for tenant
    const tenantProjectId = findTenantSupportProject(projectList, tenant)

    const collectionItemResponse = await createCollectionItem(
      accessToken,
      refreshToken,
      fvData,
      tenantProjectId,
      SUPPORT_TICKETS_SELECTOR,
      body
    )

    return collectionItemResponse
  } catch (error) {
    console.error("Error in getSupportTickets:", error)
    throw new v2.https.HttpsError("internal", error.message)
  }
})

async function createAuthSession(fvData) {
  if (fvData.pat) {
    const { access_token } = await createSessionPAT(fvData)
    return { accessToken: access_token, refreshToken: null }
  }

  const { accessToken, refreshToken } = await createSession(fvData)
  return { accessToken, refreshToken }
}

async function fetchAllProjects(accessToken, refreshToken, fvData) {
  const projects = []
  let hasMore = true
  let offset = 0
  const BATCH_SIZE = 1000

  while (hasMore) {
    const response = await getProjectList(accessToken, refreshToken, fvData, BATCH_SIZE, offset)
    projects.push(...response.items)
    hasMore = response.hasMore
    offset += BATCH_SIZE
  }

  return projects
}
