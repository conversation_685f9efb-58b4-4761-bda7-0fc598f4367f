import * as v2 from "firebase-functions/v2"
import { getFirestore, FieldValue } from "firebase-admin/firestore"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getUserList from "../../services/filevine/getUserList.js"

export const createuser = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const newUserEmailAddress = request.data.newUserEmailAddress
  const tenant = request.data.tenant

  const userEmail = request.auth.token.email
  let userCreds

  const db = getFirestore()

  await db
    .collection("users")
    .doc(userEmail)
    .get()
    .then((data) => {
      userCreds = data.data()
    })
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!userCreds.ropsAdmin) {
    throw new v2.https.HttpsError("failed-precondition", "Not Authorized")
  }

  let newUserData = null

  await db
    .collection("users")
    .doc(newUserEmailAddress)
    .get()
    .then((data) => (newUserData = data.data()))
    .catch((err) => console.error(err, "error getting data from FS"))

  if (newUserData) {
    newUserData.tenantAccessList.push(tenant)
  } else {
    let fvCreds

    await db
      .collection(tenant)
      .doc("fv-creds")
      .get()
      .then((data) => (fvCreds = data.data()))
      .catch((err) => console.error(err))

    let accessToken

    await createSessionPAT(fvCreds)
      .then((data) => {
        accessToken = data.access_token
      })
      .catch((err) => console.error(err, "Invalid PAT"))

    await getUserList(accessToken, "", fvCreds)
      .then((data) => {
        const obj = {}
        for (let i = 0; i < data.items.length; i++) {
          if (newUserEmailAddress === data.items[i].user.email) {
            obj.firstmame = data.items[i].user.firstName
            obj.fvUserId = data.items[i].user.userId.native
            obj.tenant = tenant
            if (data.items[i].user?.lastName) {
              obj.lastName = data.items[i].user.lastName
            }
            break
          }
        }
        newUserData = obj
      })
      .catch((err) => console.error(err, "error getting user list"))
  }

  if (!newUserData.fvUserId) {
    throw new v2.https.HttpsError(
      "failed-precondition",
      "No email address found associated with a user in this tenant"
    )
  }

  await db.collection("users").doc(newUserEmailAddress).set(newUserData, { merge: true })

  await db
    .collection(tenant)
    .doc("fv-creds")
    .set(
      {
        acceptedUsers: FieldValue.arrayUnion(newUserData.fvUserId)
      },
      { merge: true }
    )
    .catch((err) => console.error(err))

  return
})
