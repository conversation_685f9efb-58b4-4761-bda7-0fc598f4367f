/**
 * LogFormDataStandalone.js
 *
 * This file contains all the logic required to capture and log changes in forms.
 * It includes methods to connect to Filevine, read changes, and all helper functions
 * used in the process.
 *
 * Dependencies required (from package.json):
 * - dotenv: ^16.3.1
 * - node-fetch (if using Node.js < 18) or built-in fetch (Node.js >= 18)
 * - crypto (built-in Node.js module)
 */

require("dotenv").config()
const crypto = require("crypto")

// ================================================
// SESSION MANAGEMENT
// ================================================

const baseURL = `https://${process.env.TENANT}.api.filevineapp.com`

const createSession = async () => {
  const apiKey = process.env.API_KEY
  const apiSecret = process.env.API_SECRET
  const timestamp = new Date().toISOString()
  const data = [apiKey, timestamp, apiSecret].join("/")
  const hash = crypto.createHash("md5").update(data).digest("hex")

  let response = await fetch(baseURL + "/session", {
    method: "POST",
    mode: "cors",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      mode: "key",
      apiKey: apiKey,
      apiHash: hash,
      apiTimestamp: timestamp
    })
  })

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: response.statusText
    })
  } else {
    return response.json()
  }
}

// ================================================
// ERROR HANDLING
// ================================================

const postSlack = async (header, message) => {
  let response = await fetch(process.env.SLACK_URL, {
    method: "POST",
    mode: "cors",
    headers: {
      "Content-type": "application/json"
    },
    body: JSON.stringify({
      blocks: [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: header,
            emoji: true
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: message
          }
        }
      ]
    })
  })

  if (response.status !== 200) {
    return Promise.reject(`${response.status} ${response.statusText} | Error at postSlack`)
  } else {
    return response.status
  }
}

const errorHandler = async (inputData, apiCall, error) => {
  const header = `${error.status} Error at ${apiCall}`
  const message = `
    ${error.status} ${error.text}
    *Project ID:* ${inputData.ProjectId} \n>
    *Section Selector:* ${inputData.ObjectId.SectionSelector ? inputData.ObjectId.SectionSelector : ""} \n>
    *User ID:* ${inputData.UserId} \n>
  `

  await postSlack(header, message)
}

// ================================================
// API ACTIONS
// ================================================

// GET TRIGGERING USER
const getTriggeringUser = async (accessToken, refreshToken, inputData) => {
  let response = await fetch(baseURL + "/core/users/" + inputData.UserId, {
    method: "GET",
    mode: "cors",
    headers: {
      Authorization: "Bearer " + accessToken,
      "x-fv-sessionid": refreshToken
    }
  })

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: response.statusText
    })
  } else {
    return response.json()
  }
}

// GET TRIGGERED FORM API CALL
const getStaticSectionData = async (accessToken, refreshToken, inputData) => {
  let response = await fetch(
    baseURL + "/core/projects/" + inputData.ProjectId + "/forms/" + inputData.ObjectId.SectionSelector,
    {
      method: "GET",
      mode: "cors",
      headers: {
        Authorization: "Bearer " + accessToken,
        "x-fv-sessionid": refreshToken
      }
    }
  )

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: response.statusText
    })
  } else {
    return response.json()
  }
}

// GET TRACKED FIELDS COLLECTION SECTION API CALL
const getTrackedFields = async (accessToken, refreshToken, inputData, offset, limit) => {
  let response = await fetch(
    baseURL +
      "/core/projects/" +
      inputData.ProjectId +
      "/collections/trackedFields?offset=" +
      offset +
      "&limit=" +
      limit,
    {
      method: "GET",
      mode: "cors",
      headers: {
        Authorization: "Bearer " + accessToken,
        "x-fv-sessionid": refreshToken
      }
    }
  )

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: response.statusText
    })
  } else {
    return response.json()
  }
}

// CREATE CASE HISTORY ITEM
const createCaseHistoryItem = async (
  accessToken,
  refreshToken,
  inputData,
  historyData,
  userFirstAndLast,
  timeOfEdit
) => {
  let caseHistoryBody = {
    dataObject: {
      field: historyData.fieldName,
      editedByUsername: userFirstAndLast,
      section: historyData.sectionName,
      editedDateTime: timeOfEdit
    }
  }

  if (historyData.fieldType === "PersonLink") {
    caseHistoryBody.dataObject.oldValue = historyData.currentValue ? historyData.currentValue.fullname : " - "
    caseHistoryBody.dataObject.newValue = historyData.newValue ? historyData.newValue.fullname : " - "
  }

  if (historyData.fieldType === "ProjectLink") {
    caseHistoryBody.dataObject.oldValue = historyData.currentValue ? historyData.currentValue.title : " - "
    caseHistoryBody.dataObject.newValue = historyData.newValue ? historyData.newValue.title : " - "
  }

  if (historyData.fieldType === "Doc") {
    caseHistoryBody.dataObject.oldValue = historyData.currentValue ? historyData.currentValue.filename : " - "
    caseHistoryBody.dataObject.newValue = historyData.newValue ? historyData.newValue.filename : " - "
  }

  if (historyData.fieldType === "Text") {
    caseHistoryBody.dataObject.oldValue = historyData.currentValue ? historyData.currentValue : " - "
    caseHistoryBody.dataObject.newValue = historyData.newValue
  }

  let response = await fetch(baseURL + "/core/projects/" + inputData.ProjectId + "/collections/caseHistory", {
    method: "POST",
    mode: "cors",
    headers: {
      Authorization: "Bearer " + accessToken,
      "x-fv-sessionid": refreshToken,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(caseHistoryBody)
  })

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: response.statusText
    })
  } else {
    return response.json()
  }
}

// UPDATE TRACKED FIELDS ITEM
const updateTrackedFieldsItem = async (accessToken, refreshToken, inputData, fieldData) => {
  let updatedTrackedFieldsBody

  if (fieldData.fieldType === "Text") {
    updatedTrackedFieldsBody = {
      dataObject: {
        currentValue: fieldData.newValue
      }
    }
  }

  if (fieldData.fieldType === "PersonLink") {
    updatedTrackedFieldsBody = {
      dataObject: {
        currentValue: "x",
        currentValueContact: fieldData.newValue ? fieldData.newValue.id : 123
      }
    }
  }

  if (fieldData.fieldType === "ProjectLink" || fieldData.fieldType === "Doc") {
    updatedTrackedFieldsBody = {
      dataObject: {
        currentValue: "x",
        currentValueProject:
          fieldData.newValue && fieldData.fieldType === "ProjectLink" ? fieldData.newValue.id : null,
        currentValueFile: fieldData.newValue && fieldData.fieldType === "Doc" ? fieldData.newValue.id : null
      }
    }
  }

  let response = await fetch(
    baseURL + "/core/projects/" + inputData.ProjectId + "/collections/trackedFields/" + fieldData.itemId,
    {
      method: "PATCH",
      mode: "cors",
      headers: {
        Authorization: "Bearer " + accessToken,
        "x-fv-sessionid": refreshToken,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(updatedTrackedFieldsBody)
    }
  )

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: response.statusText
    })
  } else {
    return response.json()
  }
}

// SECTION VISIBILITY
const toggleSectionVisibility = async (accessToken, refreshToken, projectId, sectionSelector, visible) => {
  let response = await fetch(`${baseURL}/core/projects/${projectId}/sectionVisibility`, {
    method: "POST",
    mode: "cors",
    headers: {
      Authorization: "Bearer " + accessToken,
      "x-fv-sessionid": refreshToken,
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      sectionSelector: sectionSelector,
      sectionVisibility: visible
    })
  })

  if (response.status !== 200) {
    return Promise.reject({
      status: response.status,
      text: ""
    })
  } else {
    return response.status
  }
}

// ================================================
// ERROR REPEATER
// ================================================

const slackMessage = async (inputData, fieldData, apiCall, error) => {
  const header = `${error.status} Error at ${apiCall}`
  const message = `
      ${error.status} ${error.text}
      *Project ID:* ${inputData.ProjectId} \n>
      *Section Selector:* ${inputData.ObjectId.SectionSelector ? inputData.ObjectId.SectionSelector : ""} \n>
      *User ID:* ${inputData.UserId} \n>
      *Item ID:* ${fieldData.itemId}
      *Field Selector:* ${fieldData.fieldSelector}
      *Field Type:* ${fieldData.fieldType}
      *Old Value:* ${fieldData.currentValue}
      *New Value:* ${fieldData.newValue}
  `
  await postSlack(header, message)
}

const retry = async (trackedFieldsList, inputData) => {
  let formData
  let accessToken
  let refreshToken

  await createSession()
    .then((data) => {
      console.log("=== New Session ===")
      accessToken = data.accessToken
      refreshToken = data.refreshToken
    })
    .catch((error) => {
      console.log(error, "Error Creating Session from Update Tracked Field Retry")
      errorHandler(inputData, "Error Creating Session from Update Tracked Field Retry", error)
    })

  await toggleSectionVisibility(accessToken, refreshToken, inputData.ProjectId, "trackedFields", "Visible")
    .then((data) => {
      console.log(data, " <- Status | tracked fields visible")
    })
    .catch((error) => {
      console.log(
        error,
        `Toggle Tracked Field Section Visible in Project: ${inputData.ProjectId} from Add Tracked Field to All Projects`
      )
      errorHandler(
        inputData,
        `Toggle Tracked Field Section Visible in Project: ${inputData.ProjectId} from Add Tracked Field to All Projects`,
        error
      )
    })

  await getStaticSectionData(accessToken, refreshToken, inputData)
    .then((data) => {
      console.log(data, "get form")
      formData = data
    })
    .catch((error) => {
      console.log(error, "Get Static Section Data from Static Section Update")
      errorHandler(inputData, "Get Static Section Data from Static Section Update", error)
    })

  for (let i = 0; i < trackedFieldsList.length; i++) {
    const trackedFieldItem = trackedFieldsList[i]
    if (trackedFieldItem.dataObject.sectionSelector === inputData.ObjectId.SectionSelector) {
      const item = {
        itemId: trackedFieldItem.itemId.native,
        sectionSelector: trackedFieldItem.dataObject.sectionSelector,
        sectionName: trackedFieldItem.dataObject.sectionName,
        fieldSelector: trackedFieldItem.dataObject.fieldSelector,
        fieldName: trackedFieldItem.dataObject.fieldName,
        fieldType: trackedFieldItem.dataObject.fieldType,
        history: trackedFieldItem.dataObject.history
      }

      // TEXT
      if (
        item.fieldType === "Currency" ||
        item.fieldType === "PlainDecimal" ||
        item.fieldType === "Integer" ||
        item.fieldType === "Percent" ||
        item.fieldType === "CalculatedCurrency" ||
        item.fieldType === "CalculatedPlainDecimal" ||
        item.fieldType === "CalculatedInteger" ||
        item.fieldType === "CalculatedPercent" ||
        item.fieldType === "Dropdown" ||
        item.fieldType === "Text" ||
        item.fieldType === "String" ||
        item.fieldType === "Url"
      ) {
        item.currentValue = trackedFieldItem.dataObject.currentValue
        item.newValue = formData[trackedFieldItem.dataObject.fieldSelector]
        item.fieldType = "Text"

        if (item.currentValue != item.newValue) {
          console.log("TEXT ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          console.log(item, "ITEM DATA TEST")
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
            console.log(error, "update tracked field fail")
          })
        }
      }

      // DATE
      if (item.fieldType === "Date" || item.fieldType === "IncidentDate") {
        if (formData[trackedFieldItem.dataObject.fieldSelector]) {
          const newValueSplit = formData[trackedFieldItem.dataObject.fieldSelector].slice(0, 10).split("-")
          item.newValue = newValueSplit[1] + "/" + newValueSplit[2] + "/" + newValueSplit[0]
        } else {
          item.newValue = "-"
        }
        item.currentValue = trackedFieldItem.dataObject.currentValue
        item.fieldType = "Text"

        if (item.currentValue != item.newValue) {
          console.log("DATE ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
          })
        }
      }

      // YES/NO AND MULTISELECT
      if (item.fieldType === "Boolean" || item.fieldType === "MultiSelectList") {
        if (formData[trackedFieldItem.dataObject.fieldSelector] !== null) {
          item.newValue = String(formData[trackedFieldItem.dataObject.fieldSelector])
        } else {
          item.newValue = "-"
        }
        item.currentValue = trackedFieldItem.dataObject.currentValue
        item.fieldType = "Text"

        if (item.currentValue !== item.newValue) {
          console.log("BOOLEAN ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
          })
        }
      }

      // PERSON
      if (item.fieldType === "PersonLink") {
        item.currentValue = trackedFieldItem.dataObject.currentValueContact
        item.newValue = formData[trackedFieldItem.dataObject.fieldSelector]

        if ((!item.currentValue && item.newValue) || (item.currentValue && !item.newValue)) {
          console.log("CONTACT ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
          })
        }

        if (item.currentValue && item.newValue && item.currentValue.id != item.newValue.id) {
          console.log("CONTACT ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
          })
        }
      }

      // PROJECT LINK
      if (item.fieldType === "ProjectLink") {
        item.currentValue = trackedFieldItem.dataObject.currentValueProject
        item.newValue = formData[trackedFieldItem.dataObject.fieldSelector]

        if ((!item.currentValue && item.newValue) || (item.currentValue && !item.newValue)) {
          console.log("PROJECT LINK ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
          })
        }
        if (item.currentValue && item.newValue && item.currentValue.id != item.newValue.id) {
          console.log("PROJECT LINK ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item).catch((error) => {
            slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
          })
        }
      }
    }
  }

  await toggleSectionVisibility(accessToken, refreshToken, inputData.ProjectId, "trackedFields", "Hidden")
    .then((data) => {
      console.log(data, " <- Status | tracked fields hidden")
    })
    .catch((error) => {
      console.log(
        error,
        `Toggle Tracked Field Section Hidden in Project: ${inputData.ProjectId} from Add Tracked Field to All Projects`
      )
      errorHandler(
        inputData,
        `Toggle Tracked Field Section Hidden in Project: ${inputData.ProjectId} from Add Tracked Field to All Projects`,
        error
      )
    })
}

const errorWait = async (trackedFieldsList, inputData) => {
  let wait = 35000
  setTimeout(() => {
    retry(trackedFieldsList, inputData)
  }, wait)
}

// ================================================
// MAIN FUNCTION
// ================================================

/**
 * Main function to log form data changes
 * @param {string} accessToken - Filevine API access token
 * @param {string} refreshToken - Filevine API refresh token
 * @param {object} inputData - Data about the form being modified
 * @param {string} timeOfEdit - Timestamp of when the edit occurred
 */
const logFormData = async (accessToken, refreshToken, inputData, timeOfEdit) => {
  const limit = 1000
  let offset = 0
  let trackedFieldsList = []
  let userFirstAndLast
  let formData
  let runErrorWait = false

  console.log(inputData, "inputData")

  await toggleSectionVisibility(accessToken, refreshToken, inputData.ProjectId, "trackedFields", "Visible")
    .then((data) => console.log(data, "toggle form visible"))
    .catch((error) => {
      console.log(error, "Toggle Tracked Field Section Visible from Static Section Update")
      errorHandler(inputData, "Toggle Tracked Field Section Visible from Static Section Update", error)
    })

  // CALL GET TRIGGERING USER
  await getTriggeringUser(accessToken, refreshToken, inputData)
    .then((data) => {
      userFirstAndLast = data.user.firstName + " " + data.user.lastName
      console.log(userFirstAndLast)
    })
    .catch((error) => {
      console.log(error, "Get Triggering User from Static Section Update")
      errorHandler(inputData, "Get Triggering User from Static Section Update", error)
    })

  await getStaticSectionData(accessToken, refreshToken, inputData)
    .then((data) => {
      formData = data
    })
    .catch((error) => {
      console.log(error, "Get Static Section Data from Static Section Update")
      errorHandler(inputData, "Get Static Section Data from Static Section Update", error)
    })

  const getAllTrackedFields = async (accessToken, refreshToken, inputData, offset, limit) => {
    await getTrackedFields(accessToken, refreshToken, inputData, offset, limit)
      .then(async (data) => {
        trackedFieldsList.push(...data.items)
        if (data.hasMore) {
          offset = offset + limit
          await getAllTrackedFields(accessToken, refreshToken, inputData, offset, limit)
        }
      })
      .catch((error) => {
        console.log(error, "Get All Tracked Fields from Static Section Update")
        errorHandler(inputData, "Get All Tracked Fields from Static Section Update", error)
      })
  }

  await getAllTrackedFields(accessToken, refreshToken, inputData, offset, limit)

  const logCaseHistoryChange = async (accessToken, refreshToken, inputData, item) => {
    await createCaseHistoryItem(accessToken, refreshToken, inputData, item, userFirstAndLast, timeOfEdit)
      .then((data) => {
        console.log(data.dataObject.newValue, "created item data")
      })
      .catch((error) => {
        console.log(error, "Log Case History Item from Static Section Update")
        errorHandler(inputData, "Log Case History Item from Static Section Update", error)
      })

    await updateTrackedFieldsItem(accessToken, refreshToken, inputData, item)
      .then((data) => {
        console.log(data.dataObject.currentValue, "updated item data")
      })
      .catch((error) => {
        console.log(error, "Update Tracked Fields Item from Static Section Update")
        console.log(item, "item data")
        if (error.status < 500) {
          slackMessage(inputData, item, "Update Tracked Fields Item from Static Section Update", error)
        }
        runErrorWait = true
      })
  }

  for (let i = 0; i < trackedFieldsList.length; i++) {
    const trackedFieldItem = trackedFieldsList[i]
    if (trackedFieldItem.dataObject.sectionSelector === inputData.ObjectId.SectionSelector) {
      const item = {
        itemId: trackedFieldItem.itemId.native,
        sectionSelector: trackedFieldItem.dataObject.sectionSelector,
        sectionName: trackedFieldItem.dataObject.sectionName,
        fieldSelector: trackedFieldItem.dataObject.fieldSelector,
        fieldName: trackedFieldItem.dataObject.fieldName,
        fieldType: trackedFieldItem.dataObject.fieldType,
        history: trackedFieldItem.dataObject.history
      }

      // TEXT
      if (
        item.fieldType === "Currency" ||
        item.fieldType === "PlainDecimal" ||
        item.fieldType === "Integer" ||
        item.fieldType === "Percent" ||
        item.fieldType === "CalculatedCurrency" ||
        item.fieldType === "CalculatedPlainDecimal" ||
        item.fieldType === "CalculatedInteger" ||
        item.fieldType === "CalculatedPercent" ||
        item.fieldType === "Dropdown" ||
        item.fieldType === "Text" ||
        item.fieldType === "String" ||
        item.fieldType === "Url"
      ) {
        item.currentValue = trackedFieldItem.dataObject.currentValue
        item.newValue = formData[trackedFieldItem.dataObject.fieldSelector]
        item.fieldType = "Text"

        if (item.currentValue != item.newValue) {
          console.log("TEXT ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }
      }

      // DATE
      if (item.fieldType === "Date" || item.fieldType === "IncidentDate") {
        if (formData[trackedFieldItem.dataObject.fieldSelector]) {
          const newValueSplit = formData[trackedFieldItem.dataObject.fieldSelector].slice(0, 10).split("-")
          item.newValue = newValueSplit[1] + "/" + newValueSplit[2] + "/" + newValueSplit[0]
        } else {
          item.newValue = "-"
        }
        item.currentValue = trackedFieldItem.dataObject.currentValue
        item.fieldType = "Text"

        if (item.currentValue != item.newValue) {
          console.log("DATE ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }
      }

      // YES/NO AND MULTISELECT
      if (item.fieldType === "Boolean" || item.fieldType === "MultiSelectList") {
        if (formData[trackedFieldItem.dataObject.fieldSelector] !== null) {
          item.newValue = String(formData[trackedFieldItem.dataObject.fieldSelector])
        } else {
          item.newValue = "-"
        }
        item.currentValue = trackedFieldItem.dataObject.currentValue
        item.fieldType = "Text"

        if (item.currentValue !== item.newValue) {
          console.log("BOOLEAN ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }
      }

      // PERSON
      if (item.fieldType === "PersonLink") {
        item.currentValue = trackedFieldItem.dataObject.currentValueContact
        item.newValue = formData[trackedFieldItem.dataObject.fieldSelector]

        if ((!item.currentValue && item.newValue) || (item.currentValue && !item.newValue)) {
          console.log("CONTACT ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }

        if (item.currentValue && item.newValue && item.currentValue.id != item.newValue.id) {
          console.log("CONTACT ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }
      }

      // PROJECT LINK
      if (item.fieldType === "ProjectLink") {
        item.currentValue = trackedFieldItem.dataObject.currentValueProject
        item.newValue = formData[trackedFieldItem.dataObject.fieldSelector]

        if ((!item.currentValue && item.newValue) || (item.currentValue && !item.newValue)) {
          console.log("PROJECT LINK ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }
        if (item.currentValue && item.newValue && item.currentValue.id != item.newValue.id) {
          console.log("PROJECT LINK ITEM - ", item.currentValue, " has been changed to ", item.newValue)
          await logCaseHistoryChange(accessToken, refreshToken, inputData, item)
        }
      }
    }
  }

  await toggleSectionVisibility(accessToken, refreshToken, inputData.ProjectId, "trackedFields", "Hidden")
    .then((data) => console.log(data, "toggle form hidden"))
    .catch((error) => {
      console.log(error, "Toggle Tracked Field Section Hidden from Static Section Update")
      errorHandler(inputData, "Toggle Tracked Field Section Hidden from Static Section Update", error)
    })

  if (runErrorWait) {
    errorWait(trackedFieldsList, inputData)
  }

  trackedFieldsList = []
}

// Export the main function and any other functions that might be needed externally
module.exports = {
  logFormData,
  createSession,
  errorHandler,
  postSlack
}
